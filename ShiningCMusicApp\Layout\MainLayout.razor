﻿@inherits LayoutComponentBase
@using ShiningCMusicApp.Services

@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<div class="main-layout">
    <!-- Navigation Menu Component -->
    <NavMenu @ref="NavMenuRef" />

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <!-- Mobile Menu Toggle - Always visible on mobile -->
            <button class="menu-toggle d-lg-none" @onclick="() => NavMenuRef?.ToggleSidebar()">
                ☰
            </button>

            <!-- User Info -->
            <div class="user-info ms-auto">
                <AuthorizeView>
                    <Authorized>
                        <div class="d-flex align-items-center">
                            <span class="text-dark me-3">Welcome, @context.User.Identity?.Name</span>
                            <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </div>
                    </Authorized>
                </AuthorizeView>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            @Body
        </div>
    </div>
</div>

@code {
    private NavMenu? NavMenuRef;

    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }


}
