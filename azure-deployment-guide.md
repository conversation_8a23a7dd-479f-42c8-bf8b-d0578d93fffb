# Azure App Service Deployment Guide - Shining C Music App

## Overview
This guide provides step-by-step instructions to deploy your Blazor WebAssembly application with ASP.NET Core API backend to Azure App Service in a single deployment.

## Prerequisites
1. **Azure CLI**: Install from https://docs.microsoft.com/en-us/cli/azure/install-azure-cli
2. **Azure Subscription**: Active Azure subscription
3. **.NET 8.0 SDK**: Installed on your machine
4. **PowerShell**: For running deployment scripts

## Architecture
- **Frontend**: Blazor WebAssembly (served as static files)
- **Backend**: ASP.NET Core Web API with IdentityServer4
- **Database**: Azure SQL Database (already configured)
- **Hosting**: Single Azure App Service (API serves both API endpoints and Blazor static files)

## Step-by-Step Deployment

### Step 1: Login to Azure
```bash
az login
```

### Step 2: Set Your Subscription (if you have multiple)
```bash
az account list --output table
az account set --subscription "Your-Subscription-ID"
```

### Step 3: Create Azure Resources
Run the PowerShell script to create all necessary Azure resources:

```powershell
.\deploy-azure.ps1 -ResourceGroupName "rg-shiningcmusic" -AppServiceName "shiningcmusic-app" -Location "East US"
```

**Parameters:**
- `ResourceGroupName`: Name for your resource group
- `AppServiceName`: Name for your App Service (must be globally unique)
- `Location`: Azure region (e.g., "East US", "West Europe")

### Step 4: Deploy Your Application
Run the deployment script to publish and deploy your application:

```powershell
.\publish-to-azure.ps1 -ResourceGroupName "rg-shiningcmusic" -AppServiceName "shiningcmusic-app"
```

### Step 5: Verify Deployment
1. Navigate to your App Service URL: `https://your-app-name.azurewebsites.net`
2. Test API endpoints: `https://your-app-name.azurewebsites.net/api`
3. Check application logs in Azure Portal if needed

## Configuration Details

### App Service Settings
The deployment automatically configures:
- **ConnectionStrings__MusicSchool**: Your Azure SQL Database connection
- **ApiBaseUrl**: App Service URL for API calls
- **ASPNETCORE_ENVIRONMENT**: Set to "Production"

### Blazor Configuration
The `appsettings.json` in wwwroot is updated to point to your Azure API:
```json
{
  "SyncfusionLicense": "your-license-key",
  "ApiBaseUrl": "https://your-app-name.azurewebsites.net/api"
}
```

## Important Notes

### Security Considerations
1. **Database Password**: Currently hardcoded in connection string. Consider using:
   - Azure Key Vault for production
   - Managed Identity for database access
   - Environment variables for sensitive data

2. **CORS Policy**: Currently allows all origins. Restrict in production:
   ```csharp
   policy.WithOrigins("https://your-app-name.azurewebsites.net")
   ```

3. **HTTPS**: Ensure RequireHttpsMetadata is true in production

### Performance Optimization
1. **App Service Plan**: Upgrade from F1 (Free) to Standard or Premium for production
2. **Application Insights**: Add for monitoring and diagnostics
3. **CDN**: Consider Azure CDN for static Blazor files

### Troubleshooting
1. **Check Application Logs**:
   ```bash
   az webapp log tail --resource-group rg-shiningcmusic --name shiningcmusic-app
   ```

2. **Enable Detailed Errors**: In Azure Portal → Configuration → General Settings → Detailed Error Messages

3. **Database Connection**: Verify Azure SQL Database firewall allows Azure services

## Alternative: GitHub Actions Deployment

For continuous deployment, consider setting up GitHub Actions:

1. Create `.github/workflows/azure-deploy.yml`
2. Add Azure credentials as GitHub secrets
3. Automatic deployment on push to main branch

## Cost Considerations
- **Free Tier (F1)**: Good for testing, limited resources
- **Basic (B1)**: ~$13/month, better for small production apps
- **Standard (S1)**: ~$56/month, includes staging slots and auto-scaling

## Next Steps After Deployment
1. Set up custom domain (if needed)
2. Configure SSL certificate
3. Set up monitoring and alerts
4. Implement backup strategy
5. Configure staging environments
