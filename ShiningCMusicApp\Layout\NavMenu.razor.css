/* Syncfusion Sidebar Customization */
::deep .e-sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    z-index: 1000;
}

::deep .e-sidebar .e-sidebar-overlay {
    z-index: 999;
}

/* Desktop Styles */
@media (min-width: 992px) {
    /* When sidebar is open on desktop with Push type, Syncfusion will handle the margin */
    ::deep .e-sidebar {
        position: fixed !important;
        z-index: 1000;
    }

    /* No backdrop on desktop */
    ::deep .e-sidebar-overlay {
        display: none !important;
    }
}

/* Sidebar Content */
.sidebar-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar-header {
    background-color: rgba(0,0,0,0.4);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    flex-shrink: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

/* Navigation Items */
.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 0.95rem;
    font-weight: 400;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
}

.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    font-weight: 500;
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    ::deep .e-sidebar {
        z-index: 1051 !important;
    }

    /* Custom mobile backdrop */
    .mobile-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000 !important;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transition: none;
        cursor: pointer;
    }

    /* Show backdrop when sidebar is open on mobile */
    .mobile-backdrop.show {
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
    }
}
