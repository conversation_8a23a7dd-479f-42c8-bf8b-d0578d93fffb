# Azure Deployment Script for Shining C Music App
# Run this script from the repository root directory

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$AppServiceName,
    
    [Parameter(Mandatory=$true)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory=$false)]
    [string]$SubscriptionId
)

# Set subscription if provided
if ($SubscriptionId) {
    az account set --subscription $SubscriptionId
}

Write-Host "Creating Azure resources..." -ForegroundColor Green

# Create Resource Group
Write-Host "Creating resource group: $ResourceGroupName" -ForegroundColor Yellow
az group create --name $ResourceGroupName --location $Location

# Create App Service Plan (Free tier for testing, change to Standard or Premium for production)
$AppServicePlanName = "$AppServiceName-plan"
Write-Host "Creating App Service Plan: $AppServicePlanName" -ForegroundColor Yellow
az appservice plan create --name $AppServicePlanName --resource-group $ResourceGroupName --sku F1 --is-linux

# Create App Service
Write-Host "Creating App Service: $AppServiceName" -ForegroundColor Yellow
az webapp create --resource-group $ResourceGroupName --plan $AppServicePlanName --name $AppServiceName --runtime "DOTNETCORE:8.0"

# Configure App Settings
Write-Host "Configuring App Settings..." -ForegroundColor Yellow

# Get the App Service URL
$AppServiceUrl = "https://$AppServiceName.azurewebsites.net"

# Set application settings
az webapp config appsettings set --resource-group $ResourceGroupName --name $AppServiceName --settings `
    "ConnectionStrings__MusicSchool=Server=tcp:shining.database.windows.net,1433;Initial Catalog=MusicSchool;Persist Security Info=False;User ID=sam;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" `
    "ApiBaseUrl=$AppServiceUrl/" `
    "ASPNETCORE_ENVIRONMENT=Production"

Write-Host "Azure resources created successfully!" -ForegroundColor Green
Write-Host "App Service URL: $AppServiceUrl" -ForegroundColor Cyan
Write-Host "Next: Run the deployment script to publish your application" -ForegroundColor Yellow
