/* Main Layout Structure */
.main-layout {
    height: 100vh;
    overflow: hidden;
}

/* Desktop Styles */
@media (min-width: 992px) {
    /* Hide mobile toggle on desktop */
    .menu-toggle.d-lg-none {
        display: none !important;
    }
}

/* Main Content Area */
.main-content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    transition: margin-left 0.3s ease;
}

/* Top Bar */
.top-bar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    flex-shrink: 0;
    z-index: 100;
}

.menu-toggle {
    background: transparent;
    border: none;
    font-size: 1.8rem;
    color: #495057;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex !important;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    opacity: 0.8;
    font-family: Arial, sans-serif;
    line-height: 1;
}

.menu-toggle:hover {
    background-color: rgba(0,0,0,0.08);
    color: #212529;
    opacity: 1;
    transform: scale(1.05);
}

.menu-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    opacity: 1;
}

.menu-toggle:active {
    transform: scale(0.95);
    background-color: rgba(0,0,0,0.12);
}

/* Sidebar Toggle Button - Removed since desktop sidebar is always open */

.user-info {
    display: flex;
    align-items: center;
}

/* Page Content */
.page-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

/* Desktop Styles */
@media (min-width: 992px) {
    .main-content {
        margin-left: 0; /* Let Syncfusion handle the margin with Push type */
        transition: margin-left 0.3s ease;
    }

    /* Hide mobile toggle on desktop */
    .menu-toggle.d-lg-none {
        display: none !important;
    }

    /* When sidebar is open on desktop with Push type, Syncfusion will handle the margin */
    ::deep .e-sidebar {
        position: fixed !important;
        z-index: 1000;
    }

    /* Force no backdrop on desktop - immediate CSS override */
    ::deep .e-sidebar-overlay {
        display: none !important;
        background-color: transparent !important;
        opacity: 0 !important;
        pointer-events: none !important;
        visibility: hidden !important;
        z-index: -1 !important;
    }

    /* Also target any backdrop that might be created dynamically */
    ::deep .e-overlay {
        display: none !important;
        background-color: transparent !important;
        opacity: 0 !important;
        pointer-events: none !important;
        visibility: hidden !important;
    }
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    .main-content {
        margin-left: 0;
    }

    .page-content {
        padding: 1rem;
    }

    /* Mobile toggle button - always visible */
    .menu-toggle.d-lg-none {
        display: flex !important;
    }

    ::deep .e-sidebar {
        z-index: 1001;
    }

    /* Custom mobile backdrop */
    .mobile-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000 !important;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transition: none;
        cursor: pointer;
    }

    /* Show backdrop when sidebar is open on mobile */
    .mobile-backdrop.show {
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
    }
}
