@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Navigations
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<!-- Custom Mobile Backdrop -->
<div class="mobile-backdrop @((!IsDesktop && IsSidebarOpen) ? "show" : "")" @onclick="CloseMobileSidebar"></div>

<!-- Syncfusion Sidebar -->
<SfSidebar @ref="SidebarObj"
           ID="mainSidebar"
           Width="250px"
           Type="SidebarType.Auto"
           Position="SidebarPosition.Left"
           @bind-IsOpen="@IsSidebarOpen"
           EnableDock="false"
           MediaQuery="(min-width: 992px)"
           Animate="false"
           EnablePersistence="true">
    <ChildContent>
        <div class="sidebar-content">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="d-flex align-items-center p-3">
                    <i class="bi bi-music-note-beamed me-2 text-white"></i>
                    <span class="text-white fw-bold">Shining C Music</span>
                </div>
            </div>

            <!-- Navigation Menu -->
            <div class="sidebar-nav">
                <AuthorizeView Roles="Administrator">
                    <Authorized>
                        <div class="nav-item">
                            <a href="/" class="nav-link @GetActiveClass("/")" @onclick="@(() => NavigateAndClose("/"))">
                                <i class="bi bi-house-door-fill me-2"></i> Home
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/lessons" class="nav-link @GetActiveClass("/lessons")" @onclick="@(() => NavigateAndClose("/lessons"))">
                                <i class="bi bi-calendar-event me-2"></i> Lessons
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/tutors" class="nav-link @GetActiveClass("/tutors")" @onclick="@(() => NavigateAndClose("/tutors"))">
                                <i class="bi bi-person-fill me-2"></i> Tutors
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/students" class="nav-link @GetActiveClass("/students")" @onclick="@(() => NavigateAndClose("/students"))">
                                <i class="bi bi-people-fill me-2"></i> Students
                            </a>
                        </div>
                        <div class="nav-item">
                            <a href="/admin" class="nav-link @GetActiveClass("/admin")" @onclick="@(() => NavigateAndClose("/admin"))">
                                <i class="bi bi-gear-fill me-2"></i> Admin
                            </a>
                        </div>
                    </Authorized>
                </AuthorizeView>
                <AuthorizeView Roles="Tutor,Student">
                    <Authorized>
                        <div class="nav-item">
                            <a href="/lessons" class="nav-link @GetActiveClass("/lessons")" @onclick="@(() => NavigateAndClose("/lessons"))">
                                <i class="bi bi-calendar-event me-2"></i> My Lessons
                            </a>
                        </div>
                    </Authorized>
                </AuthorizeView>
            </div>
        </div>
    </ChildContent>
</SfSidebar>

@code {
    private SfSidebar? SidebarObj;
    private bool IsDesktop = true;
    private bool IsSidebarOpen = true;

    [Parameter] public EventCallback<bool> OnSidebarToggle { get; set; }

    protected override void OnInitialized()
    {
        IsDesktop = true;
        IsSidebarOpen = true;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await CheckScreenSize();
                await JSRuntime.InvokeVoidAsync("addResizeListener", DotNetObjectReference.Create(this));
            }
            catch (Exception ex)
            {
                IsDesktop = true;
                IsSidebarOpen = true;
            }
            finally
            {
                StateHasChanged();
            }
        }
    }

    [JSInvokable]
    public async Task OnWindowResize()
    {
        try
        {
            await CheckScreenSize();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            // Ignore resize errors
        }
    }

    private async Task CheckScreenSize()
    {
        try
        {
            await Task.Delay(100);
            var width = await JSRuntime.InvokeAsync<int>("eval", "window.innerWidth");
            var wasDesktop = IsDesktop;
            IsDesktop = width >= 992;

            if (IsDesktop)
            {
                IsSidebarOpen = true;
            }
        }
        catch (Exception ex)
        {
            IsDesktop = true;
            IsSidebarOpen = true;
        }
    }

    public void ToggleSidebar()
    {
        if (!IsDesktop)
        {
            IsSidebarOpen = !IsSidebarOpen;
        }
    }

    private void CloseMobileSidebar()
    {
        if (!IsDesktop && IsSidebarOpen)
        {
            IsSidebarOpen = false;
        }
    }

    private void NavigateAndClose(string url)
    {
        Navigation.NavigateTo(url);
        
        if (!IsDesktop)
        {
            IsSidebarOpen = false;
        }
    }

    private string GetActiveClass(string path)
    {
        var currentPath = Navigation.ToBaseRelativePath(Navigation.Uri);
        if (string.IsNullOrEmpty(currentPath) && path == "/")
            return "active";

        return currentPath.StartsWith(path.TrimStart('/')) ? "active" : "";
    }

    public void Dispose()
    {
        try
        {
            JSRuntime.InvokeVoidAsync("eval", "blazorMainLayoutRef = null;");
        }
        catch
        {
            // Ignore errors during disposal
        }
    }
}
