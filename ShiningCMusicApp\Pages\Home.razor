@page "/"
@attribute [Authorize(Roles = "Administrator")]

<PageTitle>Shining C Music School</PageTitle>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            <div class="text-center mb-5">
                <h1 class="display-4">🎵 Shining C Music School</h1>
                <p class="lead">Welcome to our music lesson management system</p>
            </div>

            <div class="row justify-content-center mb-4">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">Lesson Time Table</h5>
                            <p class="card-text">View and manage music lesson schedules for tutors and students.</p>
                            <a href="/lessons" class="btn btn-primary btn-lg">
                                <i class="fas fa-calendar-alt"></i> Open Scheduler
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4 d-flex">
                    <div class="card flex-fill">
                        <div class="card-body text-center d-flex flex-column">
                            <h6 class="card-title">👨‍🏫 Manage Tutors</h6>
                            <p class="card-text flex-grow-1">Add, edit, and manage tutor information</p>
                            <a href="/tutors" class="btn btn-outline-primary mt-auto">
                                <i class="fas fa-user"></i> Manage Tutors
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 d-flex">
                    <div class="card flex-fill">
                        <div class="card-body text-center d-flex flex-column">
                            <h6 class="card-title">👨‍🎓 Manage Students</h6>
                            <p class="card-text flex-grow-1">Add, edit, and manage student information</p>
                            <a href="/students" class="btn btn-outline-primary mt-auto">
                                <i class="fas fa-users"></i> Manage Students
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 d-flex">
                    <div class="card flex-fill">
                        <div class="card-body text-center d-flex flex-column">
                            <h6 class="card-title">⚙️ Admin Management</h6>
                            <p class="card-text flex-grow-1">Manage system configuration including subjects and locations</p>
                            <a href="/admin" class="btn btn-outline-primary mt-auto">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        </div>
                    </div>
                </div>
@*                 <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="card-title">📅 Schedule Lessons</h6>
                            <p class="card-text">Create and manage lesson appointments</p>
                        </div>
                    </div>
                </div> *@
            </div>
        </div>
    </div>
</div>

@code {
    // Authentication is handled by [Authorize] attribute
}
